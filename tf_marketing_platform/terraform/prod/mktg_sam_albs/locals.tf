data "aws_security_group" "apigee_sg" {
  filter {
    name   = "tag:Name"
    values = ["sfly:prod:standard:app:apigee_fronted:alb"]
  }
}

data "aws_subnets" "standard_subnets" {
  filter {
    name   = "tag:Name"
    values = [
      "prod-app_standard.us-east-1a",
      "prod-app_standard.us-east-1b",
      "prod-app_standard.us-east-1c"
    ]
  }
}

data "aws_acm_certificate" "certificate" {
  domain      = "*.internal.shutterfly.com"
  statuses    = ["ISSUED"]
}

locals {
  apigee_security_group = data.aws_security_group.apigee_sg.id

  standard_app_subnets = data.aws_subnets.standard_subnets.ids

  stage                   = "prod"
  certificate_arn         = data.aws_acm_certificate.certificate.arn
  route53_zone_id         = "Z1VUAVT4Z7YLAP"
  env                     = "prod"

  lambda_functions = {
    mktg-image-proc       = "mktg-image-proc-v2-prod"
    mktg-unsubscribe      = "sfly-aws-mktg-unsubscribe-v2-prod"
    beacon-pixel          = "beacon-pixel-v2-prod"
    subscription-service  = "sfly-aws-mktg-sub-unsub-service-v2-prod"
    share-collage         = "sfly-aws-mktg-share-collage-v2-prod"
    personalized-promos   = "personalized-promos-v2-prod"
    user-memories-feed    = "user-memories-feed-v2-prod"
    recommendations       = "recommendations-v2-prod"
    verifications2        = "verifications2-v2-prod"
    rbp-be                = "rbp-be-prod-v2-prod"
    popup-configurations  = "popup-configurations-v2-prod"
    segments              = "segments-v2-prod"
    ambassador-confirms   = "mktg-ambassador-confirmations-v2-prod"
    dynamic-components    = "email-dynamic-components-v2-prod"
    apc-component         = "apc-component-prod"
  }
}
