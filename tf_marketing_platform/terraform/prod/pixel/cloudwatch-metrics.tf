resource "aws_cloudwatch_log_group" "beacon_pixel" {
  name              = "/aws/lambda/beacon-pixle-prod"
  retention_in_days = 30
  tags              = {}
  tags_all          = {}
}

#resource "aws_cloudwatch_log_group" "beacon_pixel_v2" {
#  name = "/aws/lambda/beacon-pixel-v2-prod"
#  retention_in_days = 30
#  tags              = {}
#  tags_all          = {}
#}

resource "aws_cloudwatch_log_group" "action_iq_events_stream" {
  name              = "/aws/lambda/action-iq-events-stream-prod"
  retention_in_days = 30
  tags              = {}
  tags_all          = {}
}

resource "aws_cloudwatch_log_metric_filter" "MobileAndroidEventsCount" {
  log_group_name = aws_cloudwatch_log_group.beacon_pixel.name
  name           = "MobileAndroidEventsCount"
  pattern        = "\"counted_log\" \"posts-mobile_v2\"  \"platform: android\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-AndroidCount"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}
resource "aws_cloudwatch_log_metric_filter" "FailedMobileCalls" {
  log_group_name = aws_cloudwatch_log_group.beacon_pixel.name
  name           = "FailedMobileCalls"
  pattern        = "\"counted_log\" \"posts-mobile_v2\" \"status:\" -\"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-FailedMobileCalls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "MobileEventsCount" {
  log_group_name = aws_cloudwatch_log_group.beacon_pixel.name
  name           = "MobileEventsCount"
  pattern        = "\"counted_log\" \"posts-mobile_v2\" -\"status\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-MobileCount"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "SuccessfulMobileCalls" {
  log_group_name = aws_cloudwatch_log_group.beacon_pixel.name
  name           = "SuccessfulMobileCalls"
  pattern        = "\"counted_log\" \"posts-mobile_v2\" \"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-SuccessfulMobileCalls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "FailedWebCalls" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "FailedWebCalls"
  pattern        = "\"counted_log\" \"posts-web\" \"status:\" -\"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-FailedWebCalls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "SuccessfulWebCalls" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "SuccessfulWebCalls"
  pattern        = "\"counted_log\" \"posts-web\" \"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-SuccessfulWebCalls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "WebEventsCount" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "WebEventsCount"
  pattern        = "\"counted_log\" \"posts-web\" -\"status\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-WebCount"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "FailedProspectCalls" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "FailedProspectCalls"
  pattern        = "\"counted_log\" \"posts-prospect\" \"status:\" -\"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-FailedProspectCalls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "ProspectEventsCount" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "ProspectEventsCount"
  pattern        = "\"counted_log\" \"posts-prospect\" -\"status\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-ProspectsCount"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "SuccessfulProspectCalls" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "SuccessfulProspectCalls"
  pattern        = "\"counted_log\" \"posts-prospect\" \"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-SuccessfulProspectCalls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "CartEventsCount" {
  log_group_name = aws_cloudwatch_log_group.beacon_pixel.name
  name           = "CartEventsCount"
  pattern        = "\"counted_log\" \"posts-cart-events\" -\"status\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-CartCount"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "FailedCartCalls" {
  log_group_name = aws_cloudwatch_log_group.beacon_pixel.name
  name           = "FailedCartCalls"
  pattern        = "\"counted_log\" \"posts-cart-events\" \"status:\" -\"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-FailedCartCalls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "SuccessfulCartCalls" {
  log_group_name = aws_cloudwatch_log_group.beacon_pixel.name
  name           = "SuccessfulCartCalls"
  pattern        = "\"counted_log\" \"posts-cart-events\" \"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-SuccessfulCartCalls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_abandon_browse_count" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQAbandonBrowseInvocations"
  pattern        = "counted_log ABANDONBROWSE is being processed"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "ActionIQ-AbandonBrowse-Invocations"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_abandon_cart_count" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQAbandonCartInvocations"
  pattern        = "counted_log ABANDONCART is being processed"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "ActionIQ-AbandonCart-Invocations"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_post_purchase_count" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQPostPurchaseInvocations"
  pattern        = "counted_log POSTPURCHASE is being processed"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "ActionIQ-PostPurchase-Invocations"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_abandon_browse_success" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQAbandonBrowseSuccess"
  pattern        = "counted_log ABANDONBROWSE was processed successfully"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "ActionIQ-AbandonBrowse-Success"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_abandon_cart_success" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQAbandonCartSuccess"
  pattern        = "counted_log ABANDONCART was processed successfully"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "ActionIQ-AbandonCart-Success"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_post_purchase_success" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQPostPurchaseSuccess"
  pattern        = "counted_log POSTPURCHASE was processed successfully"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "ActionIQ-PostPurchase-Success"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "ABB_window_sessions" {
  log_group_name = "ecs-${var.env}-PlatformConsumer-live-analytics-tools"
  name           = "AbandonBrowse-WindowSession"
  pattern        = "[log, type=window_session_length, rule_name=\"AbandonBrowse\", value]"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "AbandonBrowse-WindowSession"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "$value"
  }
}

resource "aws_cloudwatch_log_metric_filter" "SPR_window_sessions" {
  log_group_name = "ecs-${var.env}-PlatformConsumer-live-analytics-tools"
  name           = "SavedProject-WindowSession"
  pattern        = "[log, type=window_session_length, rule_name=\"SavedProject\", value]"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "SavedProject-WindowSession"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "$value"
  }
}

resource "aws_cloudwatch_log_metric_filter" "ABC_window_sessions" {
  log_group_name = "ecs-${var.env}-PlatformConsumer-live-analytics-tools"
  name           = "AbandonCart-WindowSession"
  pattern        = "[log, type=window_session_length, rule_name=\"AbandonCart\", value]"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "AbandonCart-WindowSession"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "$value"
  }
}

resource "aws_cloudwatch_log_metric_filter" "PP_window_sessions" {
  log_group_name = "ecs-${var.env}-PlatformConsumer-live-analytics-tools"
  name           = "PostPurchase-WindowSession"
  pattern        = "[log, type=window_session_length, rule_name=\"PostPurchase\", value]"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "PostPurchase-WindowSession"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "$value"
  }
}

resource "aws_cloudwatch_log_metric_filter" "AbandonBrowse_window_sessions" {
  log_group_name = "ecs-${var.env}-PlatformConsumer-live-analytics-tools"
  name           = "AbandonBrowse-V3-WindowSessions"
  pattern        = "[log, type=window_session_length, rule_name=\"AbandonBrowse_3.0\", value]"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "AbandonBrowse-V3-WindowSession"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "$value"
  }
}

resource "aws_cloudwatch_log_metric_filter" "SavedProject_window_sessions" {
  log_group_name = "ecs-${var.env}-PlatformConsumer-live-analytics-tools"
  name           = "SavedProject-V3-WindowSessions"
  pattern        = "[log, type=window_session_length, rule_name=\"SavedProject_3.0\", value]"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "SavedProject-V3-WindowSession"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "$value"
  }
}

resource "aws_cloudwatch_log_metric_filter" "AbandonCart_window_sessions" {
  log_group_name = "ecs-${var.env}-PlatformConsumer-live-analytics-tools"
  name           = "AbandonCart-V3-WindowSessions"
  pattern        = "[log, type=window_session_length, rule_name=\"AbandonCart_3.0\", value]"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "AbandonCart-V3-WindowSession"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "$value"
  }
}

resource "aws_cloudwatch_log_metric_filter" "PostPurchase_window_sessions" {
  log_group_name = "ecs-${var.env}-PlatformConsumer-live-analytics-tools"
  name           = "PostPurchase-V3-WindowSessions"
  pattern        = "[log, type=window_session_length, rule_name=\"PostPurchase_3.0\", value]"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "PostPurchase-V3-WindowSession"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "$value"
  }
}

resource "aws_cloudwatch_log_metric_filter" "HomePageScrollEventsCount" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "HomePageScrollEvents"
  pattern        = "\"Posting request id\" \"home page scroll depth\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-HomePageScrollEvents"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "HomePageTimeEventsCount" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "HomePageTimeEvents"
  pattern        = "\"Posting request id\" \"home page time spent\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-HomePageTimeEvents"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

// Aurora Dashboard

resource "aws_cloudwatch_log_metric_filter" "AuroraMobileEventsCount" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "Mobile-3.0-Events-Count"
  pattern        = "\"counted_log\" \"posts-aurora-mobile_v2\" -\"status\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-Mobile-3.0-Count"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "AuroraSuccessfulMobileCalls" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "Successful-Mobile-3.0-Calls"
  pattern        = "\"counted_log\" \"posts-aurora-mobile_v2\" \"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-Successful-Mobile-3.0-Calls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "AuroraWebEventsCount" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "Web-3.0-Events-Count"
  pattern        = "\"counted_log\" \"posts-aurora-web\" -\"status\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-Web-3.0-Count"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "SuccessfulAuroraWebCalls" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "Successful-Web-3.0-Calls"
  pattern        = "\"counted_log\" \"posts-aurora-web\" \"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-Successful-Web-3.0-Calls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "AuroraCartEventsCount" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "Cart-3.0-Events-Count"
  pattern        = "\"counted_log\" \"posts-aurora-cart-service\" -\"status\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-Cart-3.0-Count"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "SuccessfulAuroraCartCalls" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "Successful-Cart-3.0-Calls"
  pattern        = "\"counted_log\" \"posts-aurora-cart-service\" \"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-Successful-Cart-3.0-Calls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "AuroraProjectEventsCount" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "SavedProject-3.0-Events-Count"
  pattern        = "\"counted_log\" \"posts-aurora-project-service\" -\"status\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-SavedProject-3.0-Count"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "SuccessfulAuroraProjectCalls" {
  log_group_name = "/aws/lambda/beacon-pixel-v2-prod"
  name           = "Successful-Project-3.0-Calls"
  pattern        = "\"counted_log\" \"posts-aurora-project-service\" \"status: 200\""

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "BeaconPixel-Successful-SavedProject-3.0-Calls"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_aurora_abandon_browse_count" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQ-AbandonBrowse-3.0-Invocations"
  pattern        = "counted_log ABANDONBROWSE_3.0 is being processed"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "ActionIQ-AbandonBrowse-3.0-Invocations"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_aurora_abandon_cart_count" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQ-AbandonCart-3.0-Invocations"
  pattern        = "counted_log ABANDONCART_3.0 is being processed"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "ActionIQ-AbandonCart-3.0-Invocations"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_aurora_spr_count" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQ-SavedProject-3.0-Invocations"
  pattern        = "counted_log SAVEDPROJECT_3.0 is being processed"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "ActionIQ-SavedProject-3.0-Invocations"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_aurora_post_purchase_count" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQ-PostPurchase-3.0-Invocations"
  pattern        = "counted_log POSTPURCHASE_3.0 is being processed"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "ActionIQ-PostPurchase-3.0-Invocations"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_aurora_abandon_browse_success" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQ-AbandonBrowse-3.0-Success"
  pattern        = "counted_log ABANDONBROWSE_3.0 was processed successfully"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "AbandonBrowse-3.0"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_aurora_abandon_cart_success" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQ-AbandonCart-3.0-Success"
  pattern        = "counted_log ABANDONCART_3.0 was processed successfully"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "AbandonCart-3.0"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_aurora_spr_success" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQ-SavedProject-3.0-Success"
  pattern        = "counted_log SAVEDPROJECT_3.0 was processed successfully"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "SavedProject-3.0"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_log_metric_filter" "action_iq_aurora_post_purchase_success" {
  log_group_name = aws_cloudwatch_log_group.action_iq_events_stream.name
  name           = "ActionIQ-PostPurchase-3.0-Success"
  pattern        = "counted_log POSTPURCHASE_3.0 was processed successfully"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "PostPurchase-3.0"
    namespace     = "Pixel"
    unit          = "Count"
    value         = "1"
  }
}
