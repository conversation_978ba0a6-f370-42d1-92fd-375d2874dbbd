{"Version": "2012-10-17", "Statement": [{"Action": ["kinesis:GetRecords", "kinesis:GetShardIterator", "kinesis:DescribeStream", "kinesis:DescribeStreamSummary", "kinesis:ListShards", "kinesis:ListStreams"], "Resource": "arn:aws:kinesis:us-east-1:${current_account_id}:stream/prod-project-service-stream", "Effect": "Allow", "Sid": "readStream"}, {"Sid": "writeStream", "Action": ["kinesis:<PERSON><PERSON><PERSON><PERSON>", "kinesis:PutRecords", "kinesis:DescribeStream"], "Effect": "Allow", "Resource": "arn:aws:kinesis:us-east-1:${current_account_id}:stream/mktg-i1-incoming-events"}, {"Sid": "ssmAllow", "Action": ["ssm:GetParameter"], "Effect": "Allow", "Resource": ["arn:aws:ssm:us-east-1:${current_account_id}:parameter/beacon-pixel/tl-access-key", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/beacon-pixel/tl-secret-key", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/interfacelayer/token-auth-header-key", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/tmail-service/token-auth-header-key", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/attentive_prospects_api", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/martech/ms-token-auth-header-key"]}, {"Effect": "Allow", "Action": ["lambda:InvokeFunction"], "Resource": ["arn:aws:lambda:us-east-1:${current_account_id}:function:beacon-pixle-prod"]}, {"Sid": "LambdaBasic", "Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": "*"}, {"Sid": "mskCommunication", "Effect": "Allow", "Action": ["kafka-cluster:Connect", "kafka-cluster:DescribeCluster"], "Resource": ["arn:aws:kafka:us-east-1:${current_account_id}:cluster/msk-ph1-cluster-1-prod/*"]}, {"Sid": "mskReadWrite", "Effect": "Allow", "Action": ["kafka-cluster:WriteData", "kafka-cluster:DescribeTopic"], "Resource": ["arn:aws:kafka:us-east-1:${current_account_id}:topic/msk-ph1-cluster-1-prod/*"]}, {"Sid": "mskGroupEdit", "Effect": "Allow", "Action": ["kafka-cluster:DescribeGroup"], "Resource": ["arn:aws:kafka:us-east-1:${current_account_id}:group/msk-ph1-cluster-1-prod/*"]}, {"Effect": "Allow", "Action": ["sqs:GetQueueUrl", "sqs:SendMessage", "sqs:ReceiveMessage"], "Resource": "arn:aws:sqs:us-east-1:${current_account_id}:sfly-aws-marketing-prod-prospects-to-dwh"}, {"Effect": "Allow", "Action": "cloudwatch:PutMetricData", "Resource": "*"}, {"Sid": "KafkaClusterConnectAndDescribe", "Effect": "Allow", "Action": ["kafka-cluster:Connect", "kafka-cluster:DescribeCluster"], "Resource": ["arn:aws:kafka:us-east-1:************:cluster/msk-infra-prod/5f07b703-6362-40e4-921c-8dfcbbb676d8-11"]}, {"Sid": "KafkaClusterWriteAndDescribeTopic", "Effect": "Allow", "Action": ["kafka-cluster:WriteData", "kafka-cluster:DescribeTopic"], "Resource": ["arn:aws:kafka:us-east-1:************:topic/msk-infra-prod/5f07b703-6362-40e4-921c-8dfcbbb676d8-11/sfly.beacon.pixel-events", "arn:aws:kafka:us-east-1:************:topic/msk-infra-prod/5f07b703-6362-40e4-921c-8dfcbbb676d8-11/sfly.apc.pixel-events.fct"]}, {"Sid": "KafkaClusterDescribeGroup", "Effect": "Allow", "Action": ["kafka-cluster:AlterGroup", "kafka-cluster:DescribeGroup"], "Resource": ["arn:aws:kafka:us-east-1:************:group/msk-infra-prod/5f07b703-6362-40e4-921c-8dfcbbb676d8-11/mktg_pixel_events", "arn:aws:kafka:us-east-1:************:group/msk-infra-prod/5f07b703-6362-40e4-921c-8dfcbbb676d8-11/apc_pixel_events"]}, {"Effect": "Allow", "Action": ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DescribeSubnets", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"], "Resource": "*"}, {"Effect": "<PERSON><PERSON>", "Action": ["ec2:CreateNetworkInterface", "ec2:DeleteNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DetachNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"], "Resource": "*", "Condition": {"ArnEquals": {"lambda:SourceFunctionArn": ["arn:aws:lambda:us-east-1:${current_account_id}:function:beacon-pixle-prod"]}}}]}