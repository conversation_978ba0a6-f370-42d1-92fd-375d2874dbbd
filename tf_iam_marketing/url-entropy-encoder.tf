
module "policy_url_entropy_encoder" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id
  stage_name    = var.stage_name
  policy_name   = "url-entropy-encoder"
  template      = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/url-entropy.json")
}


module "service_role_url_entropy_encoder" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias    = data.aws_iam_account_alias.current.account_alias
  account_id       = data.aws_caller_identity.current.account_id
  role_name_prefix = local.role_name_prefix_services
  role_name        = "url-entropy-encoder"

  policy_names = [
    module.policy_url_entropy_encoder.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
  ]

  assume_role_policy = data.template_file.assume_url_entropy.rendered


}


locals {
  assume_url_entropy_rendered = templatefile("${path.module}/templates/assume_role_policy/assume-svc-entropy.json", {
    stage_name = var.stage_name == "prod" ? "prod" : "preprod"
    account_id = var.stage_name == "prod" ? "************" : "************"
  })
}