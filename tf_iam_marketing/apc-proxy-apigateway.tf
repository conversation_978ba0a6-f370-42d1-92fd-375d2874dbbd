# Only deploy apc-proxy-apigateway directly to production, skip preprod
module "service_policy_apc_proxy_apigateway" {
  count = var.stage_name == "prod" ? 1 : 0

  source      = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"
  stage_name  = var.stage_name
  policy_name = "apc_proxy_apigateway"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-apc-proxy-apigateway.json")

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

}
 
module "service_role_apc_proxy_apigateway" {
  count = var.stage_name == "prod" ? 1 : 0

  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix = local.role_name_prefix_services
  role_name        = "apc_proxy_apigateway"
  assume_role      = "apigateway_assume"

  policy_names = [
    module.service_policy_apc_proxy_apigateway[0].name,
    module.standard_policies.policy_name_deny_security_critical_apis,
  ]
}