{"Version": "2012-10-17", "Statement": [{"Sid": "DynamoDBRealtimeDataAccess", "Effect": "Allow", "Action": ["dynamodb:GetItem", "dynamodb:BatchWriteItem", "dynamodb:UpdateItem", "dynamodb:DeleteItem"], "Resource": ["arn:aws:dynamodb:us-east-1:${current_account_id}:table/mktg-realtimedata-attributes"]}, {"Sid": "DynamoDBCartDataAccess", "Effect": "Allow", "Action": ["dynamodb:DeleteItem", "dynamodb:PutItem", "dynamodb:GetItem"], "Resource": ["arn:aws:dynamodb:us-east-1:${current_account_id}:table/mktg-tmp-cart-data"]}, {"Sid": "DynamoDBPixelCheckpointsAccess", "Effect": "Allow", "Action": ["dynamodb:UpdateItem"], "Resource": ["arn:aws:dynamodb:us-east-1:${current_account_id}:table/mktg-pixel-checkpoints-dev"]}, {"Sid": "SSMParameterStoreAccess", "Effect": "Allow", "Action": ["ssm:GetParameter"], "Resource": ["arn:aws:ssm:us-east-1:${current_account_id}:parameter/beacon-pixel/s2s_api_key", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/beacon-pixel/s2s_auth_token", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/beacon-pixel/next_gen_sfly_apikey_header"]}, {"Sid": "S3StreamsDataAccess", "Effect": "Allow", "Action": ["s3:ListObjects", "s3:GetObject", "s3:PutObject"], "Resource": ["arn:aws:s3:::sfly-aws-mktg-dev-streams-data", "arn:aws:s3:::sfly-aws-mktg-dev-streams-data/*"]}, {"Sid": "LambdaInvokeAccess", "Effect": "Allow", "Action": ["lambda:InvokeFunction"], "Resource": ["arn:aws:lambda:us-east-1:${current_account_id}:function:action-iq-events-stream-dev"]}, {"Sid": "MSKClusterAccess", "Effect": "Allow", "Action": ["kafka-cluster:Connect", "kafka-cluster:DescribeCluster"], "Resource": "arn:aws:kafka:us-east-1:************:cluster/msk-infra-dev/5b07cba1-2e5b-4d14-9c70-5b60848b4884-4"}, {"Sid": "MSKTopicAccess", "Effect": "Allow", "Action": ["kafka-cluster:ReadData", "kafka-cluster:DescribeTopic"], "Resource": "arn:aws:kafka:us-east-1:************:topic/msk-infra-dev/5b07cba1-2e5b-4d14-9c70-5b60848b4884-4/sfly.beacon.pixel-events"}, {"Sid": "MSKConsumerGroupAccess", "Effect": "Allow", "Action": ["kafka-cluster:AlterGroup", "kafka-cluster:DescribeGroup", "kafka:GetBootstrapBrokers"], "Resource": "arn:aws:kafka:us-east-1:************:group/msk-infra-dev/5b07cba1-2e5b-4d14-9c70-5b60848b4884-4/mktg_pixel_events"}]}