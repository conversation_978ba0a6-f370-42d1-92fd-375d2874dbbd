locals {
  lambda_functions = {
    mktg-image-proc       = "mktg-image-proc-v2-dev"
    mktg-unsubscribe      = "sfly-aws-mktg-unsubscribe-v2-dev"
    beacon-pixel          = "beacon-pixel-v2-dev"
    subscription-service  = "sfly-aws-mktg-sub-unsub-service-v2-dev"
    share-collage         = "sfly-aws-mktg-share-collage-v2-dev"
    personalized-promos   = "personalized-promos-v2-dev"
    user-memories-feed    = "user-memories-feed-v2-dev"
    recommendations       = "recommendations-v2-dev"
    verifications         = "verifications-v2-dev"
    rbp-be                = "rbp-be-stage-v2-dev"
    apc-component         = "apc-component-dev"
  }
}

resource "aws_lb" "alb" {
  for_each = local.lambda_functions

  name               = "${each.key}-${local.stage}-alb"
  internal           = true
  idle_timeout       = 180
  load_balancer_type = "application"
  security_groups    = [local.apigee_security_group]
  subnets            = local.standard_app_subnets
  enable_deletion_protection = false

  access_logs {
    bucket  = "sfly-aws-marketing-${local.env}-global-log"
    prefix  = "alb/${local.env}/${each.key}"
    enabled = true
  }

  tags = {
    Name  = "${each.key}-${local.stage}-alb"
    App   = each.key
  }
}

resource "aws_lb_listener" "https_listener" {
  for_each = local.lambda_functions

  load_balancer_arn = aws_lb.alb[each.key].arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = local.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.target_group[each.key].arn
  }

  tags = {
    Name  = "${each.key}-${local.stage}-listener"
  }
}

resource "aws_lb_target_group" "target_group" {
  for_each = local.lambda_functions

  name        = "${each.key}-${local.stage}-group"
  target_type = "lambda"

  tags = {
    Name  = "${each.key}-${local.stage}-group"
  }
}

resource "aws_lambda_alias" "lambda_alias" {
  for_each = local.lambda_functions

  name             = "latest"
  function_name    = each.value
  function_version = "$LATEST"
}

resource "aws_lambda_permission" "alb_policy" {
  for_each = local.lambda_functions

  action        = "lambda:InvokeFunction"
  function_name = each.value
  principal     = "elasticloadbalancing.amazonaws.com"
  source_arn    = aws_lb_target_group.target_group[each.key].arn
  qualifier     = aws_lambda_alias.lambda_alias[each.key].name
}


resource "aws_lb_target_group_attachment" "lambda_attachment" {
  for_each = local.lambda_functions

  target_group_arn = aws_lb_target_group.target_group[each.key].arn
  target_id        = "arn:aws:lambda:us-east-1:463166080413:function:${each.value}:latest"
}

resource "aws_route53_record" "internal_alb_record" {
  for_each = local.lambda_functions

  zone_id = local.route53_zone_id
  name    = each.key
  type    = "A"
  alias {
    name                   = aws_lb.alb[each.key].dns_name
    zone_id                = aws_lb.alb[each.key].zone_id
    evaluate_target_health = false
  }
}
