locals {
  service_name_platform_consumer = "platform-consumer-live-analytics-tools"
}

module "service_policy_svc_platform_consumer_live_analytics_tools" {
  source = "**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "svc-${local.service_name_platform_consumer}"

  template = templatefile("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-svc-platform-consumer-live-analytics-tools.json", {
    stage_name = var.stage_name
    current_account_id = data.aws_caller_identity.current.account_id
  })
}

module "service_role_platform_consumer_live_analytics_tools" {
  source = "**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix = local.role_name_prefix_services
  role_name        = local.service_name_platform_consumer

  policy_names = [
    module.service_policy_svc_platform_consumer_live_analytics_tools.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
  ]

  assume_role = "ecs-tasks_assume"
}
